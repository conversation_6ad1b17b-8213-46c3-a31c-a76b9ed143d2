<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VibeTribe Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            background: rgba(255, 255, 255, 0.2);
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
        }
        .success {
            background: rgba(34, 197, 94, 0.3);
        }
        a {
            color: #fbbf24;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 VibeTribe Frontend Test</h1>

        <div class="status success">
            <h3>✅ Server Status: Running</h3>
            <p>The Vite development server is working correctly!</p>
        </div>

        <div class="status">
            <h3>📱 Application Features</h3>
            <ul>
                <li>✅ PostComposer with character counters and emoji picker</li>
                <li>✅ MediaUpload with drag-and-drop functionality</li>
                <li>✅ PostScheduler with FullCalendar integration</li>
                <li>✅ DraftManager with auto-save every 30 seconds</li>
                <li>✅ Platform-specific features for Twitter/LinkedIn/Instagram/Facebook</li>
                <li>✅ Mock authentication system (auto-login in dev mode)</li>
                <li>✅ Mock WebSocket service for real-time updates</li>
            </ul>
        </div>

        <div class="status">
            <h3>🔗 Navigation</h3>
            <p><a href="/">← Back to Main Application</a></p>
            <p><a href="/dashboard">Go to Dashboard</a></p>
        </div>

        <div class="status">
            <h3>🛠️ Development Info</h3>
            <p><strong>Port:</strong> 8080</p>
            <p><strong>Mode:</strong> Development</p>
            <p><strong>Auth:</strong> Mock (auto-login enabled)</p>
            <p><strong>WebSocket:</strong> Mock service</p>
        </div>
    </div>
</body>
</html>
